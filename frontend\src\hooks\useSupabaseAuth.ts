import { useEffect, useState } from 'react'
import { useAuth } from '@clerk/clerk-react'
import { supabase } from '../lib/supabase'

export function useSupabaseAuth() {
  const { getToken, userId, isLoaded, isSignedIn } = useAuth()
  const [isSupabaseReady, setIsSupabaseReady] = useState(false)

  useEffect(() => {
    const setSupabaseAuth = async () => {
      if (!isLoaded) return

      if (isSignedIn && userId) {
        try {
          console.log('🔐 Setting up Supabase auth for user:', userId)

          // Try to get the JWT token from Clerk
          let token = null
          try {
            token = await getToken({ template: 'supabase' })
          } catch (tokenError: any) {
            if (tokenError.message?.includes('No JWT template exists')) {
              console.warn('⚠️ Supabase JWT template not configured in Clerk. Using fallback approach.')
              console.log('📋 Please create a JWT template named "supabase" in your Clerk dashboard')

              // For now, we'll use the user ID directly
              // This is a temporary workaround until the JWT template is set up
              setIsSupabaseReady(false)
              return
            } else {
              throw tokenError
            }
          }

          if (token) {
            console.log('✅ Got Clerk token, setting Supabase session')

            // Create a custom session for Supabase
            const { data, error } = await supabase.auth.setSession({
              access_token: token,
              refresh_token: '', // Clerk handles refresh
            })

            if (error) {
              console.error('❌ Error setting Supabase session:', error)
              setIsSupabaseReady(false)
            } else {
              console.log('✅ Supabase session set successfully:', data.session?.user?.id)
              setIsSupabaseReady(true)
            }
          } else {
            console.warn('⚠️ No token received from Clerk')
            setIsSupabaseReady(false)
          }
        } catch (error) {
          console.error('💥 Error setting Supabase auth:', error)
          setIsSupabaseReady(false)
        }
      } else {
        // Sign out from Supabase when user signs out from Clerk
        try {
          await supabase.auth.signOut()
          setIsSupabaseReady(false)
        } catch (error) {
          console.error('Error signing out from Supabase:', error)
        }
      }
    }

    setSupabaseAuth()
  }, [getToken, userId, isLoaded, isSignedIn])

  return {
    supabase,
    userId,
    isSupabaseReady,
    isAuthenticated: isSignedIn && !!userId
  }
}
