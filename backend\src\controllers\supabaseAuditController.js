const express = require('express');
const router = express.Router();
const supabaseService = require('../services/supabaseService');
const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

// Import existing analysis services
const aiModelService = require('../services/aiModelService');
const vulnerabilityDetectionService = require('../services/vulnerabilityDetectionService');
const gasOptimizationAnalyzer = require('../services/gasOptimizationAnalyzer');
const crossChainAnalyzer = require('../services/crossChainAnalyzer');

// Middleware to extract user from Supabase Auth
const extractUser = async (req, res, next) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      error: 'Missing or invalid authorization header'
    });
  }

  const token = authHeader.split(' ')[1];
  
  try {
    const { data: { user }, error } = await supabaseService.client.auth.getUser(token);
    
    if (error || !user) {
      return res.status(401).json({
        success: false,
        error: 'Invalid or expired token'
      });
    }

    req.user = user;
    next();
  } catch (error) {
    logger.error('Auth error:', error);
    return res.status(500).json({
      success: false,
      error: 'Authentication error'
    });
  }
};

// Upload and analyze contract
router.post('/contracts/upload', extractUser, async (req, res) => {
  try {
    const { contract_address, contract_code, protocol_type, chain_id, name, description } = req.body;
    
    if (!contract_code) {
      return res.status(400).json({
        success: false,
        error: 'Contract code is required'
      });
    }

    // Create contract record
    const contractResult = await supabaseService.createContract({
      user_id: req.user.id,
      contract_address: contract_address || `unknown-${uuidv4()}`,
      contract_code,
      protocol_type: protocol_type || 'unknown',
      chain_id: chain_id || 'ethereum',
      name: name || 'Unnamed Contract',
      description: description || ''
    });

    if (!contractResult.success) {
      return res.status(500).json({
        success: false,
        error: 'Failed to save contract'
      });
    }

    // Start audit process
    const auditResult = await supabaseService.createAuditResult({
      contract_id: contractResult.data.id,
      user_id: req.user.id,
      audit_type: 'full',
      status: 'processing'
    });

    if (!auditResult.success) {
      return res.status(500).json({
        success: false,
        error: 'Failed to create audit record'
      });
    }

    // Start analysis asynchronously
    performContractAnalysis(auditResult.data.id, contractResult.data, req.user.id);

    res.json({
      success: true,
      data: {
        contract: contractResult.data,
        audit: auditResult.data
      }
    });

  } catch (error) {
    logger.error('Contract upload error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Get contract scan results
router.get('/contracts/:contractId/results', extractUser, async (req, res) => {
  try {
    const { contractId } = req.params;

    const auditResult = await supabaseService.admin
      .from('audit_results')
      .select(`
        *,
        contracts(*),
        vulnerabilities(*)
      `)
      .eq('contract_id', contractId)
      .eq('user_id', req.user.id)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (auditResult.error) {
      return res.status(404).json({
        success: false,
        error: 'Audit result not found'
      });
    }

    res.json({
      success: true,
      data: auditResult.data
    });

  } catch (error) {
    logger.error('Get results error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Analyze contract by address
router.post('/contracts/analyze-address', extractUser, async (req, res) => {
  try {
    const { address, chain = 'ethereum' } = req.body;

    if (!address) {
      return res.status(400).json({
        success: false,
        error: 'Contract address is required'
      });
    }

    // TODO: Implement contract fetching from blockchain
    // For now, return placeholder
    res.json({
      success: false,
      error: 'Address analysis not yet implemented in Supabase version'
    });

  } catch (error) {
    logger.error('Address analysis error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Get user's audit history
router.get('/audit/history', extractUser, async (req, res) => {
  try {
    const { limit = 10, offset = 0 } = req.query;

    const result = await supabaseService.getUserAuditResults(
      req.user.id,
      parseInt(limit),
      parseInt(offset)
    );

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch audit history'
      });
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    logger.error('Audit history error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Get user's contracts
router.get('/contracts', extractUser, async (req, res) => {
  try {
    const { limit = 10, offset = 0 } = req.query;

    const result = await supabaseService.getUserContracts(
      req.user.id,
      parseInt(limit),
      parseInt(offset)
    );

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch contracts'
      });
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    logger.error('Get contracts error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Multi-agent analysis endpoint
router.post('/ai/multi-agent-analysis', extractUser, async (req, res) => {
  try {
    const { contract_code, analysis_type = 'comprehensive' } = req.body;

    if (!contract_code) {
      return res.status(400).json({
        success: false,
        error: 'Contract code is required'
      });
    }

    // Create temporary contract for analysis
    const contractResult = await supabaseService.createContract({
      user_id: req.user.id,
      contract_address: `temp-${uuidv4()}`,
      contract_code,
      protocol_type: 'analysis-only',
      chain_id: 'multi-chain',
      name: 'Multi-Agent Analysis',
      description: `${analysis_type} analysis`
    });

    if (!contractResult.success) {
      return res.status(500).json({
        success: false,
        error: 'Failed to create analysis record'
      });
    }

    // Create audit record
    const auditResult = await supabaseService.createAuditResult({
      contract_id: contractResult.data.id,
      user_id: req.user.id,
      audit_type: analysis_type,
      status: 'processing'
    });

    // Start multi-agent analysis
    const analysis = await performMultiAgentAnalysis(contract_code, analysis_type);

    // Update audit result
    const updateResult = await supabaseService.updateAuditResult(auditResult.data.id, {
      status: 'completed',
      results: analysis.results,
      agent_results: analysis.agentResults,
      vulnerability_score: analysis.scores.vulnerability,
      security_score: analysis.scores.security,
      gas_optimization_score: analysis.scores.gasOptimization,
      confidence_score: analysis.confidence,
      completed_at: new Date().toISOString()
    });

    // Create vulnerabilities if found
    if (analysis.vulnerabilities && analysis.vulnerabilities.length > 0) {
      const vulnerabilityData = analysis.vulnerabilities.map(vuln => ({
        audit_result_id: auditResult.data.id,
        type: vuln.type,
        severity: vuln.severity,
        title: vuln.title,
        description: vuln.description,
        line_number: vuln.lineNumber,
        code_snippet: vuln.codeSnippet,
        recommendation: vuln.recommendation,
        confidence: vuln.confidence
      }));

      await supabaseService.createVulnerabilities(vulnerabilityData);
    }

    res.json({
      success: true,
      data: {
        audit_id: auditResult.data.id,
        analysis: analysis,
        status: 'completed'
      }
    });

  } catch (error) {
    logger.error('Multi-agent analysis error:', error);
    res.status(500).json({
      success: false,
      error: 'Analysis failed'
    });
  }
});

// Perform contract analysis (async function)
async function performContractAnalysis(auditId, contract, userId) {
  try {
    const startTime = Date.now();

    // Run various analyses
    const [
      vulnerabilityAnalysis,
      gasOptimizationAnalysis,
      securityAnalysis
    ] = await Promise.all([
      vulnerabilityDetectionService.analyzeContract(contract.contract_code),
      gasOptimizationAnalyzer.analyzeContract(contract.contract_code),
      aiModelService.analyzeContractSecurity(contract.contract_code)
    ]);

    const analysisTime = Date.now() - startTime;

    // Combine results
    const combinedResults = {
      vulnerability: vulnerabilityAnalysis,
      gasOptimization: gasOptimizationAnalysis,
      security: securityAnalysis,
      analysisMetrics: {
        duration: analysisTime,
        timestamp: new Date().toISOString()
      }
    };

    // Calculate scores
    const scores = {
      vulnerability: calculateVulnerabilityScore(vulnerabilityAnalysis),
      security: calculateSecurityScore(securityAnalysis),
      gasOptimization: calculateGasScore(gasOptimizationAnalysis)
    };

    // Update audit result
    await supabaseService.updateAuditResult(auditId, {
      status: 'completed',
      results: combinedResults,
      vulnerability_score: scores.vulnerability,
      security_score: scores.security,
      gas_optimization_score: scores.gasOptimization,
      confidence_score: 0.85, // Default confidence
      analysis_duration: analysisTime,
      completed_at: new Date().toISOString()
    });

    // Extract and save vulnerabilities
    const vulnerabilities = extractVulnerabilities(vulnerabilityAnalysis, securityAnalysis);
    if (vulnerabilities.length > 0) {
      const vulnerabilityData = vulnerabilities.map(vuln => ({
        audit_result_id: auditId,
        ...vuln
      }));
      await supabaseService.createVulnerabilities(vulnerabilityData);
    }

    // Log analytics
    await supabaseService.logAnalytics({
      user_id: userId,
      event_type: 'contract_analysis_completed',
      event_data: {
        audit_id: auditId,
        contract_id: contract.id,
        analysis_duration: analysisTime,
        vulnerabilities_found: vulnerabilities.length,
        scores: scores
      }
    });

    logger.info('Contract analysis completed', { auditId, userId, analysisTime });

  } catch (error) {
    logger.error('Contract analysis failed:', error);
    
    // Update audit result with error status
    await supabaseService.updateAuditResult(auditId, {
      status: 'failed',
      results: { error: error.message },
      completed_at: new Date().toISOString()
    });
  }
}

// Multi-agent analysis function
async function performMultiAgentAnalysis(contractCode, analysisType) {
  // Implementation would use your existing AI agents
  // This is a simplified version
  
  const results = {
    security: await aiModelService.analyzeContractSecurity(contractCode),
    vulnerabilities: await vulnerabilityDetectionService.analyzeContract(contractCode),
    gasOptimization: await gasOptimizationAnalyzer.analyzeContract(contractCode)
  };

  const scores = {
    vulnerability: calculateVulnerabilityScore(results.vulnerabilities),
    security: calculateSecurityScore(results.security),
    gasOptimization: calculateGasScore(results.gasOptimization)
  };

  return {
    results,
    agentResults: results, // Detailed agent-specific results
    scores,
    confidence: 0.9,
    vulnerabilities: extractVulnerabilities(results.vulnerabilities, results.security)
  };
}

// Helper functions for score calculation
function calculateVulnerabilityScore(analysis) {
  // Implement your scoring logic
  return Math.floor(Math.random() * 100); // Placeholder
}

function calculateSecurityScore(analysis) {
  // Implement your scoring logic
  return Math.floor(Math.random() * 100); // Placeholder
}

function calculateGasScore(analysis) {
  // Implement your scoring logic
  return Math.floor(Math.random() * 100); // Placeholder
}

function extractVulnerabilities(vulnAnalysis, securityAnalysis) {
  // Extract vulnerabilities from analysis results
  // This is a placeholder implementation
  return [];
}

module.exports = router;