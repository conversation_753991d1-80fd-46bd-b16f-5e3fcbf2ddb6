import { supabase } from '../lib/supabase'
import type { Database } from '../lib/supabase'

type Project = Database['public']['Tables']['projects']['Row']
type ProjectInsert = Database['public']['Tables']['projects']['Insert']
type ProjectUpdate = Database['public']['Tables']['projects']['Update']

export class ProjectService {
  static async createProject(project: Omit<ProjectInsert, 'user_id'>, userId: string): Promise<Project | null> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .insert({
          ...project,
          user_id: userId,
        })
        .select()
        .single()

      if (error) {
        console.error('Error creating project:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error creating project:', error)
      return null
    }
  }

  static async getUserProjects(userId: string): Promise<Project[]> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .eq('user_id', userId)
        .order('updated_at', { ascending: false })

      if (error) {
        if (error.code === '42P01') {
          console.warn('⚠️ Projects table does not exist in Supabase.')
          console.log('📋 Please create the table by running the SQL from SETUP_INSTRUCTIONS.md')
        } else {
          console.error('Error fetching projects:', error)
        }
        return []
      }

      return data || []
    } catch (error: any) {
      if (error.code === '42P01') {
        console.warn('⚠️ Projects table does not exist in Supabase.')
        console.log('📋 Please create the table by running the SQL from SETUP_INSTRUCTIONS.md')
      } else {
        console.error('Error fetching projects:', error)
      }
      return []
    }
  }

  static async updateProject(projectId: string, updates: ProjectUpdate): Promise<Project | null> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', projectId)
        .select()
        .single()

      if (error) {
        console.error('Error updating project:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error updating project:', error)
      return null
    }
  }

  static async deleteProject(projectId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('id', projectId)

      if (error) {
        console.error('Error deleting project:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Error deleting project:', error)
      return false
    }
  }

  static async getProject(projectId: string): Promise<Project | null> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .eq('id', projectId)
        .single()

      if (error) {
        console.error('Error fetching project:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error fetching project:', error)
      return null
    }
  }
}
