import { supabase } from '../lib/supabase'
import type { Database } from '../lib/supabase'

type VulnerabilityScan = Database['public']['Tables']['vulnerability_scans']['Row']
type VulnerabilityScanInsert = Database['public']['Tables']['vulnerability_scans']['Insert']

export class VulnerabilityService {
  static async createScan(
    contractAddress: string,
    network: string,
    userId: string
  ): Promise<VulnerabilityScan | null> {
    try {
      const { data, error } = await supabase
        .from('vulnerability_scans')
        .insert({
          user_id: userId,
          contract_address: contractAddress,
          network: network,
          status: 'pending',
        })
        .select()
        .single()

      if (error) {
        console.error('Error creating vulnerability scan:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error creating vulnerability scan:', error)
      return null
    }
  }

  static async updateScanResults(
    scanId: string,
    results: any,
    status: 'completed' | 'failed'
  ): Promise<VulnerabilityScan | null> {
    try {
      const { data, error } = await supabase
        .from('vulnerability_scans')
        .update({
          scan_results: results,
          status: status,
        })
        .eq('id', scanId)
        .select()
        .single()

      if (error) {
        console.error('Error updating scan results:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error updating scan results:', error)
      return null
    }
  }

  static async getUserScans(userId: string): Promise<VulnerabilityScan[]> {
    try {
      const { data, error } = await supabase
        .from('vulnerability_scans')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching vulnerability scans:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('Error fetching vulnerability scans:', error)
      return []
    }
  }

  static async getScan(scanId: string): Promise<VulnerabilityScan | null> {
    try {
      const { data, error } = await supabase
        .from('vulnerability_scans')
        .select('*')
        .eq('id', scanId)
        .single()

      if (error) {
        console.error('Error fetching vulnerability scan:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error fetching vulnerability scan:', error)
      return null
    }
  }
}
