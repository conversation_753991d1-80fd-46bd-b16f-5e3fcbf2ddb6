# Server Configuration
PORT=3001
NODE_ENV=development

# OpenRouter API Configuration
OPENROUTER_API_KEY=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
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
LLM_MODEL=google/gemma-2-9b-it

# Blockchain RPC URLs
ETHEREUM_RPC_URL=https://eth-mainnet.g.alchemy.com/v2/YOUR_ALCHEMY_KEY
POLYGON_RPC_URL=https://polygon-mainnet.g.alchemy.com/v2/YOUR_ALCHEMY_KEY
BSC_RPC_URL=https://bsc-dataseed.binance.org/

# Ethereum Testnet URLs (for testing)
SEPOLIA_RPC_URL=https://eth-sepolia.g.alchemy.com/v2/YOUR_ALCHEMY_KEY
POLYGON_MUMBAI_RPC_URL=https://polygon-mumbai.g.alchemy.com/v2/YOUR_ALCHEMY_KEY
BSC_TESTNET_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545/

# Security Configuration
JWT_SECRET=your-super-secret-jwt-key-here
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# TEE Monitor Configuration
TEE_LOG_ENABLED=true
TEE_LOG_PATH=./logs/auditLogs.json
TEE_ENCRYPTION_KEY=your-tee-encryption-key-here

# Audit Configuration
MAX_CONTRACT_SIZE_BYTES=1048576
AUDIT_TIMEOUT_MS=30000
VULNERABILITY_THRESHOLD_HIGH=80
VULNERABILITY_THRESHOLD_MEDIUM=50

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log
