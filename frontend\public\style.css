* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  html,
  body {
    height: 100%;
    width: 100%;
  }
  #main{
      position: relative;
      overflow: hidden;
      background-color: #1137ca;
  }
  @font-face {
      font-family: a;
      src: url(/jost-variable.ttf);
  }
  @font-face {
      font-family: b;
      src: url("/KFOlCnqEu92Fr1MmEU9fBBc4 (1).ttf");
  }
  @font-face {
      font-family: c;
      src: url("/KFOmCnqEu92Fr1Mu4mxK (1).ttf");
  }
  #page1
  {
      height: 100vh;
      width: 100vw;
      position: relative;
  }
  #page1>video{
      height: 100%;
      width: 100%;
      object-fit: cover;
  }
  #page1>nav{
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0px 30px;
      position: absolute;
      height: 10vh;
      width: 100vw;
  }
  #page1>nav>img{
      margin-top: -1.7vw;
      width: 9%;
  }
  #right-nav>button{
      padding: 10px 20px;
      border-radius: 50px;
      background-color: #0b48ed;
      border: 1px solid #fff;
      color: #fff;
      font-family: a;
      font-size: 15px;
  }
  .bottom-page1{
      position: absolute;
      bottom: 5%;
      height: 35vh;
      width: 50vw;
      left: 10%;
  }
  .bottom-page1>h1{
      font-family: a;
      font-size: 5vw;
      font-weight: 100;
      line-height: 1;
      color: #fff;
  }
  .bottom-page1-inner{
      position: absolute;
      bottom: 0;
      height: 35%;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-family: a;
  }
  .bottom-page1-inner>button{
      padding: 10px 20px;
      border: none;
      border-radius: 50px;
      background-color: #fff;
      color: #0b48ed;
      font-size: 16px;
      font-family: b;
  }
  .bottom-page1-inner>h4{
      font-size: 1.3vw;
      font-weight: 100;
      color: #fff;
  }
  #page2{
      display: flex;
      align-items: start;
      font-family: a;
      justify-content: center;
      flex-direction: column;
      height: 100vh;
      width: 100vw;
      position: relative;
      padding: 0vw 8vw;
      color: #fff;
      background-color: #0a3cce;
  }
  #page2>h2{
      margin-bottom: 3vw;
      font-weight: 100;
  }
  #page2>h1{
      font-weight: 100;
      line-height: 1.3;
      width: 90%;
      font-size: 4vw;
      color: #dadada69;
  }
  
  
  #page3{
      position: relative;
      height: 100vh;
      width: 100vw;
  }
  #page3>canvas{
      max-height: 100vh;
      max-width: 100vw;
      position: relative;
  }
  #page4{
      display: flex;
      align-items: start;
      justify-content: center;
      flex-direction: column;
      position: relative;
      height: 100vh;
      width: 100vw;
      background-color: #1137ca;
      font-family: a;
  }
  #page4>h3{
      margin-left: 15vw;
      margin-bottom: 2vw;
      font-weight: 100;
      color: #fff;
  }
  #page4>h1{
      margin-left: 15vw;
      width: 70%;
      font-size: 3vw;
      font-weight: 100;
      color: #ffffff81;
  }
  #page5{
      position: relative;
      height: 100vh;
      width: 100vw;
  }
  #page5>canvas{
      position: relative;
      max-width: 100vw;
      max-height: 100vh;
  }
  #page6{
      display: flex;
      align-items: start;
      justify-content: center;
      position: relative;
      height: 100vh;
      width: 100vw;
      background-color: #0a3cce;
      flex-direction: column;
      font-family: a;
  }
  #page6>h3{
      margin-left: 15vw;
      font-weight: 100;
      color: #fff;
      margin-bottom: 2vw;
  }
  #page6>h1{
      margin-left: 15vw;
      font-size: 3vw;
      width: 70%;
      font-weight: 100;
      color: #ffffff53;
  }
  #page7{
      position: relative;
      height: 100vh;
      width: 100vw;
      background-color: #0a3cce;
  }
  #page7>canvas{
      position: relative;
      max-width: 100vw;
      max-height: 100vh;
  }
  .page7-cir{
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%,-50%);
      z-index: 9;
      height: 30vw;
      width: 30vw;
      border-radius: 50%;
      border: 1px solid #fff;
  }
  .page7-cir-inner{
      height: 70%;
      width: 70%;
      border-radius: 50%;
      background-color: transparent;
      border: 1px solid #fff;
  }
  
  #page8{
      position: relative;
      height: 100vh;
      width: 100vw;
      background-color: #0a3cce;
  }
  #page8>video{
      height: 100%;
      width: 100%;
      object-fit: cover;
  }
  .page8-bottom{
      position: absolute;
      bottom: 5%;
      left: 50%;
      transform: translateX(-50%);
      text-align: center;
      font-family: a;
  }
  .page8-bottom>h1{
      color: #fff;
      margin-bottom: 2vw;
      font-size: 5vw;
      font-weight: 100;
  }
  .page8-bottom>button{
      padding: 20px 30px;
      border-radius: 50px;
      border: none;
      background-color: #fff;
      color: #0a3cce;
  }
  #page9{
      display: flex;
      position: relative;
      height: 100vh;
      width: 100vw;
      background-color: #0a3cce;
  }
  .left9{
      height: 100%;
      width: 40%;
      position: relative;
      font-family: a;
  }
  .left9>h1{
      position: absolute;
      top: 40%;
      right: 5%;
      transform: translateY(-50%);
      font-size: 5vw;
      font-weight: 100;
      color: #fff;
      line-height: 1;
  }
  .right9{
      height: 100%;
      width: 60%;
      position: relative;
  }
  .right9-center{
      height: 50%;
      width: 85%;
      border-radius: 10px;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      background-image: url(https://thisismagma.com/wp-content/uploads/2023/04/magma.wp2.cubdev.com-home-1.jpeg);
      background-size: cover;
      left: 5%;
  }
  #page10{
      position: relative;
      height: 100vh;
      width: 100vw;
      background-color: #0a3cce;
  }
  .right10{
      height: 100%;
      width: 60%;
      position: relative;
      left: 40%;
  }
  .right10-inner{
      display: flex;
      align-items: start;
      flex-direction: column;
      height: 33.3%;
      width: 100%;
      font-family: a;
      color: #fff;
  }
  .right10-inner>h1{
      font-size: 2vw;
  }
  .right10-inner>p{
      margin-top: 2vw;
      font-size: 1.3vw;
      width: 80%;
  }
  
  #page11{
      position: relative;
      height: 110vh;
      width: 100vw;
      background-color: #fff;
      font-family: a;
  }
  #page11>h1{
      position: absolute;
      top: 5%;
      left: 10%;
      font-size: 4vw;
      color: #000;
      font-weight: 100;
  }
  .page11-inner{
      display: flex;
      top: 18%;
      position: relative;
      margin-bottom: 2vw;
      height: 24%;
      width: 100%;
  }
  .left11{
      position: relative;
      height: 100%;
      width: 40%;
      left: 5%;
      border-radius: 10px;
      overflow: hidden;
  }
  .left11>img{
      height: 100%;
      width: 100%;
      object-fit: cover;
  }
  .right11{
      padding-top: 5vw;
      color: #000;
      left: 50%;
      position: absolute;
      height: 100%;
      width: 50%;
  }
  .right11>h4{
      font-weight: 100;
  }
  .right11>h1{
      font-size: 2vw;
  }
  
  
  #page12{
      position: relative;
      height: 100vh;
      width: 100vw;
      background-color: #02268e;
  }
  .page12-inner{
      position: absolute;
      top: 20%;
      right: 10%;
      color: #fff;
      font-family: a;
  }
  .page12-inner>h1{
      font-size: 5vw;
      font-weight: 100;
  }
  .page12-inner>p{
      font-size: 1.3vw;
  }
  
  
  #page13{
      position: relative;
      height: 50vh;
      width: 100vw;
      background-color: #000;
      color: #fff;
      font-family: a;
      padding: 7vw 10vw;
  }
  #page13>h1{
      font-size: 5vw;
      line-height: 1;
      font-weight: 100;
  }
  #page13>button{
      padding: 18px 30px;
      border: none;
      background-color: #0a3cce;
      color: #fff;
      border-radius: 50px;
      margin-top: 2vw;
  }
  
  
  #page14{
      position: relative;
      height: 60vh;
      width: 100vw;
      background-color: #000;
  }
  .page14-inner{
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0vw 5vw;
      font-family: a;
      height: 33.3%;
      width: 100%;
      color: #fff;
      border-top: .5px solid #ffffff5c;
      border-bottom: .5px solid #ffffff48;
  }
  .page14-inner>i{
      font-weight: 100;
      font-size: 2.4vw;
      position: relative;
      z-index: 9999;
  }
  .page14-inner>h1{
      font-size: 3vw;
      font-weight: 100;
      position: relative;
      z-index: 9999;
  }
  
  .center14{
      height: 0;
      width: 100%;
      background-color: #0a3cce;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%,-50%);
      transition: all ease .5s;
  }


  .page14-inner:hover .center14{
      height: 100%;
  }